'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListDriver } from '../api/queries';
import { CreateDriver } from '../components/create-driver';
import { DriverFilters } from '../components/driver-filters';
import { DriverTable } from '../components/driver-table';

export function DriverPage() {
   const [page, setPage] = useState(1);
   const [perPage] = useState(10);
   const [search, setSearch] = useState('');
   const [status, setStatus] = useState<string | undefined>(undefined);
   const [location, setLocation] = useState<string | undefined>(undefined);

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   const handleStatusChange = (value: string | undefined) => {
      setStatus(value);
      setPage(1);
   };

   const handleLocationChange = (value: string | undefined) => {
      setLocation(value);
      setPage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setStatus(undefined);
      setLocation(undefined);
      setPage(1);
   };

   const listDriver = useListDriver({
      page,
      perPage,
      search: search || undefined,
      status: status as 'pending' | 'active' | 'inactive' | undefined,
      location: location || undefined,
   });

   // Calculate driver counts by status
   const driverCounts = {
      pending: listDriver.data?.data?.filter(driver => driver.status === 'pending').length || 0,
      active: listDriver.data?.data?.filter(driver => driver.status === 'active').length || 0,
      inactive: listDriver.data?.data?.filter(driver => driver.status === 'inactive').length || 0,
   };

   return (
      <div className='flex flex-1 flex-col gap-4 p-4'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Drivers</h2>
            <div className='flex items-center gap-4'>
               {/* Driver Info Cards */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Pending</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full'>
                        {driverCounts.pending}
                     </span>
                  </div>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Active</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-green-700 bg-green-100 rounded-full'>
                        {driverCounts.active}
                     </span>
                  </div>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Inactive</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-red-700 bg-red-100 rounded-full'>
                        {driverCounts.inactive}
                     </span>
                  </div>
               </div>
               <CreateDriver />
            </div>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <DriverFilters
               search={search}
               status={status}
               location={location}
               onSearchChange={handleSearchChange}
               onStatusChange={handleStatusChange}
               onLocationChange={handleLocationChange}
               isLoading={listDriver.isFetching && !listDriver.isLoading}
            />

            <DriverTable
               data={listDriver.data}
               isLoading={listDriver.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={!!search || !!status || !!location}
               hasSearch={!!search}
               hasStatus={!!status}
               hasLocation={!!location}
               onClearFilters={handleClearFilters}
            />
         </Card>
      </div>
   );
}
