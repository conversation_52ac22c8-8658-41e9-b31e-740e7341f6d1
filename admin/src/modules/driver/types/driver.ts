// City interface for API responses
export interface City {
   id: string;
   name: string;
   countryId: string;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

// Driver registration and OTP types
export interface DriverRegistrationRequest {
   phoneNumber: string;
}

export interface DriverOtpVerificationRequest {
   phoneNumber: string;
   otp: string;
}

export interface DriverResendOtpRequest {
   phoneNumber: string;
}

// Updated Driver interface to match new API structure
export interface Driver {
   id: string; // Changed from number to string
   userId?: string;
   firstName: string;
   lastName: string;
   mobileNumber: string; // Changed from phoneNumber
   email: string;
   gender: 'MALE' | 'FEMALE' | 'OTHER'; // More specific type
   dob: string; // Changed from dateOfBirth
   profilePictureUrl?: string;
   cityId: string; // Changed from location
   createdAt: string;
   updatedAt: string;
   deletedAt?: string | null;
}

// API response structure for listing drivers
export interface ListDriverResponse {
   success: boolean;
   message: string;
   data: Driver[];
   meta?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
   };
   timestamp: number;
}

// API response structure for single driver
export interface DriverResponse {
   success: boolean;
   message: string;
   data: Driver;
   timestamp: number;
}

// Request for creating driver profile (after OTP verification)
export interface CreateDriverRequest {
   userId?: string;
   firstName?: string;
   lastName?: string;
   mobileNumber: string; // Required field
   email?: string;
   gender?: 'MALE' | 'FEMALE' | 'OTHER';
   dob?: string;
   profilePictureUrl?: string;
   cityId?: string;
}

// Request for updating driver profile
export interface UpdateDriverRequest {
   firstName?: string;
   lastName?: string;
   email?: string;
   gender?: 'MALE' | 'FEMALE' | 'OTHER';
   dob?: string;
   profilePictureUrl?: string;
   cityId?: string;
}

// Parameters for listing drivers with filters
export interface ListDriverParams {
   page?: number;
   limit?: number;
   sortBy?: string;
   sortOrder?: 'asc' | 'desc';
   search?: string;
   cityId?: string;
   name?: string;
   email?: string;
   phoneNumber?: string;
}

export interface DriverFilters {
   page?: number;
   perPage?: number;
   search?: string;
   status?: 'pending' | 'active' | 'inactive';
   location?: string;
}
