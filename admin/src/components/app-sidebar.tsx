'use client';
import * as React from 'react';
import { Car, LayoutDashboard, MapPin } from 'lucide-react';

import { NavMain } from '@/components/nav-main';
import { Sidebar, SidebarContent, SidebarHeader, SidebarRail } from '@/components/ui/sidebar';
import { LogoName } from './logo-name';

// Driver management data
const data = {
   user: {
      name: 'john doe',
      email: '<EMAIL>',
      avatar: '/avatars/shadcn.jpg',
   },
   navMain: [
      {
         title: 'Dashboard',
         url: '/dashboard/home',
         icon: LayoutDashboard,
         isActive: false,
      },
      {
         title: 'Drivers',
         url: '/dashboard/drivers',
         icon: Car,
         isActive: true,
      },
      {
         title: 'Cities',
         url: '/cities',
         icon: MapPin,
         isActive: false,
      },
   ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   return (
      <Sidebar className='bg-white' collapsible='icon' {...props}>
         <SidebarHeader className='bg-white'>
            <div className='flex items-center justify-center'>
               <LogoName width={85} height={85} />
            </div>
         </SidebarHeader>
         <SidebarContent className='bg-white'>
            <NavMain items={data.navMain} />
         </SidebarContent>
         <SidebarRail />
      </Sidebar>
   );
}
